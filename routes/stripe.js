const express = require('express');
const stripeObj = require('stripe');
const router = express.Router();
const mongoose = require('mongoose');
const Stripe = require('../models/stripe');
const Customer = require('../models/customer');
const Order = require('../models/order');
const Restaurant = require('../models/restaurant');
const config = require('../config');
const Zone = require('../models/zone');
const { sendEmail } = require('../helpers/email');
const { sendNotification } = require('../helpers/utilities');
const { placeOrderTemplate } = require('../helpers/templates');
const { stripeCurrencies } = require('../helpers/currencies');
const { sendNotificationToRestaurant } = require('../helpers/notifications');
const { pubsub, publishToDashboard, publishToDispatcher, ORDER_STATUS_CHANGED } = require('../helpers/pubsub');

const { transformOrder } = require('../graphql/resolvers/merge');
const bodyParser = require('body-parser');
const { sendNotificationToCustomerWeb } = require('../helpers/firebase-web-notifications');
const logger = require('../helpers/logger');

// Import WhatsApp session service for sending payment completion events
let sessionService;
try {
  sessionService = require('../whatsapp/services/sessionService');
} catch (error) {
  logger.info('WhatsApp session service not available:', error.message);
  sessionService = null;
}

var stripe;

var CURRENCY = 'EUR';
var CURRENCY_SYMBOL = '€';
var CURRENCY_MULTIPLIER = 100;
const initializeStripe = async () => {
  stripe = stripeObj(config.STRIPE.SECRET_KEY, {
      // 不指定apiVersion，使用最新的API版本
      maxNetworkRetries: 2, // 设置网络重试次数
      timeout: 30000 // 设置超时时间为30秒
    }
  );
  //TODO: change the currency to configurable
  //CURRENCY = configuration.currency;
  //CURRENCY_SYMBOL = configuration.currencySymbol;
  CURRENCY_MULTIPLIER = stripeCurrencies.find(val => val.currency === CURRENCY).multiplier;
};

/**
 * 安全配置端点 - 向前端提供必要的公开配置
 */
router.get('/config', async (req, res) => {
  try {
    // 验证请求来源
    const { orderId } = req.query;

    if (!orderId) {
      return res.status(400).json({ error: 'Order ID is required' });
    }

    // 验证订单存在
    const stripeOrder = await Stripe.findOne({ orderId });
    if (!stripeOrder) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // 获取Stripe公钥（直接从环境变量读取）
    const publishableKey = config.STRIPE.PUBLISHABLE_KEY;

    // 返回安全配置
    res.json({
      publishableKey,
      serverUrl: config.SERVER_URL,
      businessName: 'Firespoon',
      brandImage: `${config.SERVER_URL}/images/logo.png`,
      orderId,
      amount: stripeOrder.orderAmount * CURRENCY_MULTIPLIER,
      currency: CURRENCY.toLowerCase(),
      customerEmail: stripeOrder.user?.email || '',
      orderDescription: `Order #${orderId}`
    });

  } catch (error) {
    logger.error('Config endpoint error:', error);
    res.status(500).json({ error: 'Configuration loading failed' });
  }
});

router.get('/success', async (req, res) => {
  try {
    const { session_id: stripeSessionId, platform = 'mobile', orderId } = req.query;
    const order = await handleCompletedCheckoutSession(stripeSessionId);
    if (platform === 'web') {
      logger.debug('config.WEB_URL', config.WEB_URL);
      return res.redirect(config.WEB_URL + '#/' + config.ORDER_DETAIL_WEB_URL + order.id + '?clearCart=true');
    } else {
      return res.render('stripeSuccess');
    }
  } catch (error) {
    logger.error('Stripe success error:', error);
    return res.sendStatus(500);
  }
});
router.get('/failed', async (req, res) => {
  return res.render('stripeFailed');
});
router.get('/cancel', async (req, res) => {
  return res.render('stripeCancel');
});

// 退款相关端点
router.post('/refund/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    const { amount, reason, reasonText } = req.body;

    const refundService = require('../services/refundService');
    const { REFUND_TYPE } = require('../helpers/enum');

    // 这里应该验证请求权限，但为了简化暂时跳过
    const result = await refundService.initiateRefund(
      orderId,
      amount,
      reason,
      reasonText,
      amount ? REFUND_TYPE.PARTIAL : REFUND_TYPE.FULL,
      'system' // 临时使用system作为requestedBy
    );

    res.json(result);
  } catch (error) {
    logger.error('Refund API error', { error: error.message });
    res.status(400).json({ error: error.message });
  }
});

router.get('/refund/:refundId', async (req, res) => {
  try {
    const { refundId } = req.params;
    const refundService = require('../services/refundService');

    const refund = await refundService.getRefund(refundId);
    if (!refund) {
      return res.status(404).json({ error: 'Refund not found' });
    }

    res.json(refund);
  } catch (error) {
    logger.error('Get refund API error', { error: error.message });
    res.status(400).json({ error: error.message });
  }
});
router.post('/account', async (req, res) => {
  logger.debug('post.stripe/account', req.body);
  try {
    await initializeStripe();
    const { restaurantId } = req.body;
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true }
      },
      metadata: {
        restaurantId
      }
    });
    // save account.id in restaurant data, use it later to update the status of account
    // const result = await Restaurant.updateOne({ _id: restaurantId }, { stripeAccountId: account.id, stripeDetailsSubmitted: false })
    // if (result.nModified > 0) {
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${config.SERVER_URL}${'stripe/account/refresh?accountId=' + account.id}`,
      return_url: `${config.SERVER_URL}${'stripe/account/return?accountId=' + account.id}`,
      type: 'account_onboarding'
    });
    res.send(accountLink);
    return;
  } catch (error) {
    logger.error('Stripe account creation error:', error);
  }
  res.sendStatus(400);
});
router.get('/account/refresh', async (req, res) => {
  logger.debug('get.account/refresh');
  try {
    const { accountId } = req.query;
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: config.SERVER_URL + 'stripe/account/refresh?accountId=' + accountId,
      return_url: config.SERVER_URL + 'stripe/account/return?accountId=' + accountId,
      type: 'account_onboarding'
    });
    logger.debug('accountLink', accountLink);
    // res.send(accountLink)
    res.redirect(accountLink.url);
    return;
  } catch (error) {
    logger.error('Account refresh error:', error);
    res.send(error);
  }
});
router.get('/account/return', async (req, res) => {
  logger.debug('get.account/return');
  try {
    const { accountId } = req.query;
    await initializeStripe();
    const account = await stripe.accounts.retrieve(accountId);
    if (account.details_submitted) {
      await Restaurant.updateOne(
        { _id: account.metadata.restaurantId },
        { stripeAccountId: accountId, stripeDetailsSubmitted: true }
      );
    }
    return res.redirect(config.DASHBOARD_URL + 'admin/payment');
  } catch (error) {
    logger.error('Account return error:', error);
    res.send(error);
  }
});
router.delete('/account', async (req, res) => {
  logger.debug('delete.account');
  try {
    const { accountId } = req.query;
    await initializeStripe();
    const deleted = await stripe.accounts.del(accountId);
    res.send(deleted);
    return;
  } catch (error) {
    logger.error('Account deletion error:', error);
    res.send(error);
  }
});
router.get('/account', async (req, res) => {
  logger.debug('get.account');
  try {
    const { accountId } = req.query;
    await initializeStripe();
    const account = await stripe.accounts.retrieve(accountId);
    res.send(account);
    return;
  } catch (error) {
    logger.error('Account retrieval error:', error);
    res.send(error);
  }
});
router.get('/cs', async (req, res) => {
  logger.debug('stripe/cs', req.query);
  try {
    await initializeStripe();
    const { id: orderId, platform = 'mobile' } = req.query;

    const line_items = [];
    const stripeOrder = await Stripe.findOne({ orderId });

    if (!stripeOrder) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // 验证订单状态
    if (stripeOrder.paymentStatus !== 'PENDING') {
      return res.status(400).json({ error: 'Order is not in pending status' });
    }

    // 防重复支付检查
    if (stripeOrder.stripeSessionId) {
      const existingSession = await stripe.checkout.sessions.retrieve(stripeOrder.stripeSessionId);
      if (existingSession.payment_status === 'paid') {
        return res.status(400).json({ error: 'Order already paid' });
      }
    }

    const restaurant = await Restaurant.findById(stripeOrder.restaurant);

    // 重新计算并验证订单金额
    const recalculatedAmount = await recalculateOrderAmount(stripeOrder, restaurant);
    if (Math.abs(recalculatedAmount - stripeOrder.orderAmount) > 0.01) {
      logger.error('支付时金额验证失败', {
        orderId,
        stored: stripeOrder.orderAmount,
        calculated: recalculatedAmount
      });
      return res.status(400).json({ error: '订单金额验证失败，请重新下单' });
    }
    stripeOrder.items.forEach(item => {
      line_items.push({
        price_data: {
          product_data: {
            name: `${item.title} ${item.variation.title}`
          },
          unit_amount: parseInt(item.variation.price * CURRENCY_MULTIPLIER),
          currency: CURRENCY
        },
        quantity: item.quantity
      });
      item.addons.forEach(addon => {
        addon.options.map(option => {
          line_items.push({
            price_data: {
              product_data: {
                name: option.title
              },
              unit_amount: parseInt(option.price * CURRENCY_MULTIPLIER),
              currency: CURRENCY
            },
            quantity: 1
          });
        });
      });
    });
    line_items.push({
      price_data: {
        product_data: {
          name: 'Delivery Fee'
        },
        unit_amount: parseInt(stripeOrder.deliveryCharges * CURRENCY_MULTIPLIER),
        currency: CURRENCY
      },
      quantity: 1
    });

    line_items.push({
      price_data: {
        product_data: {
          name: 'Tax'
        },
        unit_amount: parseInt(stripeOrder.taxationAmount * CURRENCY_MULTIPLIER),
        currency: CURRENCY
      },
      quantity: 1
    });

    line_items.push({
      price_data: {
        product_data: {
          name: 'Tip'
        },
        unit_amount: parseInt(stripeOrder.tipping * CURRENCY_MULTIPLIER),
        currency: CURRENCY
      },
      quantity: 1
    });

    const items_amount =
      stripeOrder.orderAmount - stripeOrder.deliveryCharges - stripeOrder.taxationAmount - stripeOrder.tipping;
    const application_fee_amount =
      items_amount * (restaurant.commissionRate / 100) +
      stripeOrder.deliveryCharges +
      stripeOrder.taxationAmount +
      stripeOrder.tipping; // save application fee percent in configuration
    let success_url;
    if (platform === 'mobile') {
      success_url = `${config.SERVER_URL}stripe/success?session_id={CHECKOUT_SESSION_ID}&platform=${platform}`;
    } else if (platform === 'web') {
      success_url = `${config.SERVER_URL}stripe/success?session_id={CHECKOUT_SESSION_ID}&platform=${platform}&orderId=${stripeOrder.orderId}`;
    } else {
      success_url = `${config.SERVER_URL}stripe/success?session_id={CHECKOUT_SESSION_ID}`;
    }
    const cancel_url = platform === 'web' ? config.WEB_URL : config.SERVER_URL + 'stripe/cancel';
    logger.debug('Restaurant ID: ' + restaurant.stripeAccountId);
    // 创建支付会话配置 - 使用最新版本的Stripe SDK
    const sessionConfig = {
      line_items,
      mode: 'payment',
      success_url,
      cancel_url,
      // 明确指定支持的支付方式
      payment_method_types: ['card', 'alipay', 'paypal', 'revolut_pay'],
      // 配置特定支付方式的选项
      payment_method_options: {
        paypal: {
          // PayPal 配置选项
          capture_method: 'manual',
        },
      },  
      billing_address_collection: 'auto', // 自动收集地址信息
    };

    logger.debug('Creating Stripe Checkout session with config:', JSON.stringify(sessionConfig, null, 2));
    const session = await stripe.checkout.sessions.create(sessionConfig);
    logger.debug('Stripe Checkout session created:', JSON.stringify(session, null, 2));
    const result = await Stripe.updateOne(
      { orderId },
      {
        stripeSessionId: session.id,
        platform: platform // Set the platform field
      }
    );
    logger.debug('result', result);

    if (result.modifiedCount > 0) {
      res.status(303).redirect(session.url);
      logger.debug(session.url);
      return;
    } else {
      res.status(303).redirect(config.SERVER_URL + 'stripe/cancel');
      return;
    }
  } catch (error) {
    logger.error('Stripe error:', error);
    res.status(501).send(error);
  }
});
router.post('/webhook', bodyParser.raw({ type: 'application/json' }), async (req, res) => {
  const endpointSecret = config.STRIPE.WEBHOOK_ENDPOINT_SECRET;
  const sig = req.headers['stripe-signature'];

  let event;

  // Verify webhook signature and extract the event.
  // See https://stripe.com/docs/webhooks/signatures for more information.
  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === 'checkout.session.completed') {
    const session = event.data.object;
    logger.info('webhook checkout.session.completed', session.id);
    handleCompletedCheckoutSession(session.id);
  }
  if (event.type === 'account.updated') {
    logger.info('account.updated', event.data.object);
    const account = event.data.object;
    if (account.details_submitted) {
      await Restaurant.updateOne(
        { _id: account.metadata.restaurantId },
        { stripeAccountId: account.id, stripeDetailsSubmitted: true }
      );
    }
    logger.info('webhook account.updated', account.id);
  }

  // 处理退款相关的webhook事件
  if (event.type === 'charge.dispute.created') {
    logger.info('charge.dispute.created', event.data.object);
    // TODO: 处理争议创建
  }

  if (event.type === 'refund.created') {
    const refund = event.data.object;
    logger.info('refund.created', refund.id);
    // 退款创建时的处理（通常不需要特殊处理）
  }

  if (event.type === 'refund.updated') {
    const refund = event.data.object;
    logger.info('refund.updated', refund.id, refund.status);
    await handleRefundWebhook(refund.id, refund.status, refund);
  }

  res.json({ received: true });
});

// 处理退款webhook的函数
const handleRefundWebhook = async (stripeRefundId, status, refundData) => {
  try {
    const refundService = require('../services/refundService');
    await refundService.processRefundWebhook(stripeRefundId, status, refundData);
    logger.info('Refund webhook processed successfully', { stripeRefundId, status });
  } catch (error) {
    logger.error('Error processing refund webhook', {
      stripeRefundId,
      status,
      error: error.message
    });
  }
};

const handleCompletedCheckoutSession = async stripeSessionId => {
  const stripeOrder = await Stripe.findOne({
    stripeSessionId
  });

  if (!stripeOrder) {
    logger.error('No Stripe order found for session ID', { stripeSessionId });
    return;
  }

  // 防重复支付检查 - 检查是否已经创建了订单
  const existingOrder = await Order.findOne({ orderId: stripeOrder.orderId });
  if (existingOrder) {
    logger.warn('Duplicate payment attempt detected', {
      orderId: stripeOrder.orderId,
      stripeSessionId,
      existingOrderId: existingOrder._id,
      existingPaymentStatus: existingOrder.paymentStatus
    });
    return existingOrder;
  }

  try {
    await initializeStripe();
    const session = await stripe.checkout.sessions.retrieve(stripeSessionId);
    const paymentIntentId = session.payment_intent;
    const customer = await Customer.findById(stripeOrder.user);

    // 验证支付状态
    if (session.payment_status !== 'paid') {
      logger.error('Payment not completed', {
        stripeSessionId,
        paymentStatus: session.payment_status
      });
      return;
    }

    // 验证支付金额（Stripe金额以分为单位）
    const paidAmountInCents = session.amount_total;
    const expectedAmountInCents = Math.round(stripeOrder.orderAmount * CURRENCY_MULTIPLIER);

    if (Math.abs(paidAmountInCents - expectedAmountInCents) > 1) { // 允许1分的误差
      logger.error('Payment amount mismatch detected', {
        orderId: stripeOrder.orderId,
        stripeSessionId,
        paidAmount: paidAmountInCents / CURRENCY_MULTIPLIER,
        expectedAmount: stripeOrder.orderAmount,
        difference: (paidAmountInCents - expectedAmountInCents) / CURRENCY_MULTIPLIER
      });
      // 不创建订单，记录错误
      return;
    }

    logger.info('Payment amount verified successfully', {
      orderId: stripeOrder.orderId,
      paidAmount: paidAmountInCents / CURRENCY_MULTIPLIER,
      expectedAmount: stripeOrder.orderAmount
    });

    const itemsFood = stripeOrder.items;

    let price = 0;
    itemsFood.forEach(async item => {
      let item_price = item.variation.price;
      if (item.addons && item.addons.length) {
        const addonDetails = [];
        item.addons.forEach(({ options }) => {
          // console.log("options:",options)
          options.forEach(option => {
            item_price = item_price + option.price;
            // eslint-disable-next-line no-tabs
            addonDetails.push(`${option.title}	 ${CURRENCY_SYMBOL}${option.price}`);
          });
        });
      }
      price += item_price * item.quantity;
      // eslint-disable-next-line no-tabs
      return `${item.quantity} x ${item.title}${
        item.variation.title ? `(${item.variation.title})` : ''
      } ${CURRENCY_SYMBOL}${item.variation.price}`;
    });

    if (stripeOrder.coupon) {
      price = price - (stripeOrder.coupon.discount / 100) * price;
    }
    const restaurant = await Restaurant.findById(stripeOrder.restaurant);
    /*  const zone = await Zone.findOne({
    isActive: true,
    location: {
      $geoIntersects: { $geometry: restaurant.location }
    }
  })
  if (!zone) {
    throw new Error('Delivery zone not found')
  }*/
    // 获取客户信息以获取customerId和customerPhone
    const order = new Order({
      //    zone: stripeOrder.zone,
      restaurant: stripeOrder.restaurant,
      restaurantId: stripeOrder.restaurantId,
      restaurantName: restaurant.name,
      restaurantBrand: restaurant.restaurantBrand,
      restaurantBrandId: restaurant.restaurantBrandId,
      customerId: customer.customerId,
      customerPhone: customer.phone,
      items: stripeOrder.items,
      deliveryAddress: stripeOrder.deliveryAddress,
      deliveryAddressId: stripeOrder.deliveryAddressId,
      deliveryCoordinates: stripeOrder.deliveryCoordinates,
      orderId: stripeOrder.orderId,
      orderStatus: 'PENDING',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: stripeOrder.orderAmount,
      orderAmount: stripeOrder.orderAmount,
      deliveryCharges: stripeOrder.deliveryCharges,
      completionTime: new Date(Date.now() + restaurant.deliveryTime * 60 * 1000),
      tipping: stripeOrder.tipping,
      taxationAmount: stripeOrder.taxationAmount,
      orderDate: stripeOrder.orderDate,
      isPickedUp: stripeOrder.isPickedUp,
      expectedTime: stripeOrder.expectedTime,
      instructions: stripeOrder.instructions,
      paymentId: paymentIntentId // 修正：保存支付意图ID
    });
    const result = await order.save();

    // 修正：更新临时Stripe记录的状态和支付信息
    stripeOrder.paymentStatus = 'PAID';
    stripeOrder.paymentId = paymentIntentId;

    await stripeOrder.save();

    // 更新客户的订单历史记录
    // 创建订单简要信息对象
    const orderBrief = {
      orderId: result._id,
      amount: result.orderAmount,
      restaurantId: result.restaurantId,
      restaurantName: result.restaurantName,
      restaurantBrandId: restaurant.restaurantBrandId,
      restaurantBrand: restaurant.restaurantBrand,
      orderStatus: result.orderStatus,
      orderDate: result.orderDate
    };

    // 将订单添加到客户的订单历史记录中
    customer.orderHistory.unshift(orderBrief); // 添加到数组开头，使最新订单显示在前面

    // 保存客户信息
    await customer.save();

    logger.debug('Updated customer order history:', {
      customerId: customer.customerId,
      orderHistoryLength: customer.orderHistory.length
    });

    const placeOrder_template = await placeOrderTemplate([
      order.orderId,
      itemsFood,
      stripeOrder.isPickedUp ? restaurant.address : result.deliveryAddress,
      `${CURRENCY_SYMBOL} ${Number(price).toFixed(2)}`,
      `${CURRENCY_SYMBOL} ${result.tipping.toFixed(2)}`,
      `${CURRENCY_SYMBOL} ${result.taxationAmount.toFixed(2)}`,
      `${CURRENCY_SYMBOL} ${result.deliveryCharges.toFixed(2)}`,
      `${CURRENCY_SYMBOL} ${result.orderAmount.toFixed(2)}`,
      CURRENCY_SYMBOL
    ]);

    //  sendEmail(customer.email, 'Order Placed', '', placeOrder_template)
    sendNotificationToRestaurant(result.restaurantId, result);
    const transformedOrder = await transformOrder(result);
    const orderStatusChanged = {
      userId: customer.customerId,
      order: transformedOrder,
      origin: 'new'
    };
    pubsub.publish(ORDER_STATUS_CHANGED, {
      orderStatusChanged: orderStatusChanged
    });
    publishToDashboard(result.restaurantId.toString(), transformedOrder, 'new');
    //  publishToDispatcher(transformedOrder)
    sendNotification(result.orderId);
    // 客户可能没有Web通知令牌，所以需要检查
    if (customer.notificationTokenWeb) {
      sendNotificationToCustomerWeb(customer.notificationTokenWeb, 'Order placed', `Order ID ${result.orderId}`);
    }
    logger.debug('sendNotificationToCustomerWeb');

    // Send payment completion event to WhatsApp session if available
    if (sessionService && stripeOrder.platform === 'ws') {
      // Find the WhatsApp dialogue ID associated with this order
      // For WhatsApp orders, we use the orderId to identify the session
      const orderId = stripeOrder.orderId;

      // Get customer phone to help identify the session
      const customerPhone = customer.phone;

      logger.debug('Sending payment completion event to WhatsApp session', {
        orderId,
        customerPhone,
        restaurantId: stripeOrder.restaurantId,
        restaurantName: restaurant.name
      });

      // Find all active sessions for this customer
      const sessions = await sessionService.findSessionsByCustomerPhone(customerPhone);

      if (sessions && sessions.length > 0) {
        // Find the session with matching orderId in currentOrderState
        // This is the correct location where orderId should be stored
        let matchingSession = sessions.find(session => {
          const sessionOrderId = session.context?.currentOrderState?.orderId;
          const matches = sessionOrderId === orderId;
          logger.debug('Comparing orderId', {
            sessionOrderId,
            stripeOrderId: orderId,
            matches,
            dialogueId: session.dialogueId
          });
          return matches;
        });

        // Second try: Find session with matching restaurantId
        if (!matchingSession && stripeOrder.restaurantId) {
          logger.debug('No orderId match found, trying to match by restaurantId', {
            restaurantId: stripeOrder.restaurantId
          });
          matchingSession = sessions.find(session => {
            return (
              session.context?.selectedRestaurantRef?.id === stripeOrder.restaurantId ||
              session.context?.currentOrder?.restaurantId === stripeOrder.restaurantId
            );
          });
        }

        // Last resort: If still no match found but we have sessions, use the most recently updated one
        if (!matchingSession && sessions.length > 0) {
          logger.debug('No matching session found by orderId or restaurantId, using most recent active session');
          // Sort sessions by updatedAt timestamp (most recent first)
          const sortedSessions = [...sessions].sort((a, b) => {
            const dateA = new Date(a.updatedAt || 0);
            const dateB = new Date(b.updatedAt || 0);
            return dateB - dateA;
          });
          matchingSession = sortedSessions[0];
        }

        if (matchingSession) {
          // Send payment complete event to the session queue
          await sessionService.queueSessionEvent(matchingSession.dialogueId, {
            type: 'PAYMENT_COMPLETE',
            data: {
              orderId,
              paymentMethod: 'STRIPE',
              paymentStatus: 'completed',
              amount: stripeOrder.orderAmount,
              timestamp: new Date().toISOString(),
              // Include additional order details to ensure complete information
              restaurantId: stripeOrder.restaurantId,
              restaurantName: restaurant.name,
              customerPhone: customer.phone,
              deliveryAddress: stripeOrder.deliveryAddress,
              items: stripeOrder.items.map(item => ({
                title: item.title,
                quantity: item.quantity,
                price: item.variation.price
              }))
            }
          });

          logger.debug('Payment completion event sent to WhatsApp session', { dialogueId: matchingSession.dialogueId });
        } else {
          logger.debug('No matching WhatsApp session found for order', { orderId });
        }
      } else {
        logger.debug('No active WhatsApp sessions found for customer', { customerPhone });
      }
    }

    return result;
  } catch (error) {
    logger.error('Error in handleCompletedCheckoutSession', { stripeSessionId, error: error.message, stack: error.stack });
    // 不返回任何内容，让调用者知道处理失败
  }
};

/**
 * 重新计算订单金额以验证完整性
 * @param {Object} stripeOrder - Stripe订单对象
 * @param {Object} restaurant - 餐厅对象
 * @returns {Number} 计算的订单总金额
 */
async function recalculateOrderAmount(stripeOrder, restaurant) {
  let total = 0;

  // 重新计算商品金额
  for (const item of stripeOrder.items) {
    let itemPrice = item.variation.price;

    // 计算插件价格
    if (item.addons && item.addons.length > 0) {
      for (const addon of item.addons) {
        for (const option of addon.options) {
          itemPrice += option.price;
        }
      }
    }

    total += itemPrice * item.quantity;
  }

  // 添加其他费用
  total += stripeOrder.deliveryCharges || 0;
  total += stripeOrder.taxationAmount || 0;
  total += stripeOrder.tipping || 0;

  // 处理优惠券（如果有）
  if (stripeOrder.coupon && stripeOrder.coupon._id) {
    const Coupon = require('../models/coupon');
    const coupon = await Coupon.findById(stripeOrder.coupon._id);
    if (coupon && coupon.isActive) {
      if (coupon.couponType === 'fixed') {
        total -= coupon.discount;
      } else {
        total -= (coupon.discount / 100) * total;
      }
    }
  }

  return Math.max(0, total); // 确保金额不为负
}

module.exports = router;
