/**
 * Link Generator Unit Tests
 * 测试链接生成器的核心功能
 */

const linkGenerator = require('../../../../whatsapp/utils/linkGenerator');

describe('LinkGenerator', () => {
  describe('generateMenuLink()', () => {
    test('should generate menu link with token and restaurantURL', () => {
      const token = 'session-123';
      const restaurantURL = 'app.firespoon.com/restaurant1';

      const link = linkGenerator.generateMenuLink(token, restaurantURL);

      expect(link).toBe(`https://${restaurantURL}/${token}/`);
    });

    test('should handle different domains', () => {
      const token = 'session-456';
      const restaurantURL = 'staging.firespoon.com/restaurant2';

      const link = linkGenerator.generateMenuLink(token, restaurantURL);

      expect(link).toBe(`https://${restaurantURL}/${token}/`);
    });

    test('should handle special characters in token', () => {
      const token = 'session-123+special/chars=';
      const restaurantURL = 'app.firespoon.com/restaurant3';

      const link = linkGenerator.generateMenuLink(token, restaurantURL);

      expect(link).toBe(`https://${restaurantURL}/${token}/`);
    });

    test('should throw error when restaurantURL is empty', () => {
      const token = 'session-123';
      const restaurantURL = '';

      expect(() => {
        linkGenerator.generateMenuLink(token, restaurantURL);
      }).toThrow('Restaurant URL is required for generating menu link');
    });

    test('should throw error when restaurantURL is null', () => {
      const token = 'session-123';
      const restaurantURL = null;

      expect(() => {
        linkGenerator.generateMenuLink(token, restaurantURL);
      }).toThrow('Restaurant URL is required for generating menu link');
    });
  });

  describe('generateAddressLink()', () => {
    test('should generate address link with token and restaurantURL', () => {
      const token = 'session-123';
      const restaurantURL = 'app.firespoon.com/restaurant1';

      const link = linkGenerator.generateAddressLink(token, restaurantURL);

      expect(link).toBe(`https://${restaurantURL}/${token}/`);
    });

    test('should handle different domains', () => {
      const token = 'session-456';
      const restaurantURL = 'staging.firespoon.com/restaurant2';

      const link = linkGenerator.generateAddressLink(token, restaurantURL);

      expect(link).toBe(`https://${restaurantURL}/${token}/`);
    });

    test('should handle special characters in token', () => {
      const token = 'session-123+special/chars=';
      const restaurantURL = 'app.firespoon.com/restaurant3';

      const link = linkGenerator.generateAddressLink(token, restaurantURL);

      expect(link).toBe(`https://${restaurantURL}/${token}/`);
    });

    test('should throw error when restaurantURL is empty', () => {
      const token = 'session-123';
      const restaurantURL = '';

      expect(() => {
        linkGenerator.generateAddressLink(token, restaurantURL);
      }).toThrow('Restaurant URL is required for generating address link');
    });

    test('should throw error when restaurantURL is null', () => {
      const token = 'session-123';
      const restaurantURL = null;

      expect(() => {
        linkGenerator.generateAddressLink(token, restaurantURL);
      }).toThrow('Restaurant URL is required for generating address link');
    });
  });

});
