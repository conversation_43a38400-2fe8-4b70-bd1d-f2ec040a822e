const config = require('../../config');

const generateMenuLink = (token, restaurantURL) => {
  if (!restaurantURL) {
    throw new Error('Restaurant URL is required for generating menu link');
  }
  return `https://${restaurantURL}${token}/`;
};

const generateAddressLink = (token, restaurantURL) => {
  if (!restaurantURL) {
    throw new Error('Restaurant URL is required for generating address link');
  }
  return `https://${restaurantURL}a/${token}/`;
};

module.exports = { generateMenuLink, generateAddressLink };
